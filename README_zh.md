# AI 研究助手 - 全栈 LangGraph 应用

这个项目展示了一个使用 React 前端和 LangGraph 驱动的后端智能体的全栈应用程序。该智能体旨在对用户查询进行全面研究，通过动态生成搜索词、使用 Google 搜索查询网络、反思结果以识别知识差距，并迭代优化搜索，直到能够提供有充分支持和引用的答案。该应用程序支持 **Google Gemini** 和 **OpenAI** 模型，允许用户选择他们偏好的 AI 提供商。

![Gemini Fullstack LangGraph](./app.png)

## 功能特性

- 💬 使用 React 前端和 LangGraph 后端的全栈应用程序
- 🧠 由 LangGraph 智能体驱动的高级研究和对话 AI
- 🤖 **多提供商 AI 支持**：在 Google Gemini 和 OpenAI 模型之间选择
- 🔍 使用可配置 AI 模型的动态搜索查询生成
- 🌐 通过 Google Search API 集成的网络研究
- 🤔 反思推理以识别知识差距并优化搜索
- 📄 生成带有引用来源的答案
- ⚙️ **灵活配置**：支持自定义基础 URL 和 API 密钥
- 🔄 开发期间前端和后端的热重载

## 项目结构

项目分为两个主要目录：

- `frontend/`：包含使用 Vite 构建的 React 应用程序
- `backend/`：包含 LangGraph/FastAPI 应用程序，包括研究智能体逻辑

## 开始使用：开发和本地测试

按照以下步骤在本地运行应用程序进行开发和测试。

**1. 前置要求：**

- Node.js 和 npm（或 yarn/pnpm）
- Python 3.8+
- **API 密钥**：后端智能体至少需要一个 AI 提供商的 API 密钥

**API 配置：**

1. 导航到 `backend/` 目录
2. 通过复制 `backend/.env.example` 文件创建名为 `.env` 的文件
3. 配置您偏好的 AI 提供商：

**Google Gemini（默认）：**
```bash
GEMINI_API_KEY="YOUR_GEMINI_API_KEY"
```

**OpenAI：**
```bash
OPENAI_API_KEY="YOUR_OPENAI_API_KEY"
# 可选：OpenAI 兼容 API 的自定义基础 URL
OPENAI_BASE_URL="https://api.openai.com/v1"
```

**同时使用两个提供商：**
```bash
GEMINI_API_KEY="YOUR_GEMINI_API_KEY"
OPENAI_API_KEY="YOUR_OPENAI_API_KEY"
```

**2. 安装依赖：**

**后端：**
```bash
cd backend
pip install .
```

**前端：**
```bash
cd frontend
npm install
```

**3. 运行开发服务器：**

**后端和前端：**
```bash
make dev
```

这将运行后端和前端开发服务器。打开浏览器并导航到前端开发服务器 URL（例如，`http://localhost:5173/app`）。

_或者，您可以分别运行后端和前端开发服务器。对于后端，在 `backend/` 目录中打开终端并运行 `langgraph dev`。后端 API 将在 `http://127.0.0.1:2024` 可用。它还会打开一个浏览器窗口到 LangGraph UI。对于前端，在 `frontend/` 目录中打开终端并运行 `npm run dev`。前端将在 `http://localhost:5173` 可用。_

## AI 模型选择

应用程序支持多个 AI 提供商和模型：

### 可用模型

**Google Gemini 模型：**
- `gemini-2.0-flash` - 快速高效的查询生成
- `gemini-2.5-flash-preview-04-17` - 增强的推理能力
- `gemini-2.5-pro-preview-05-06` - 最先进的 Gemini 模型

**OpenAI 模型：**
- `gpt-4o` - 最新的 GPT-4 优化模型
- `gpt-4o-mini` - 更快、更经济的版本
- `gpt-4-turbo` - 高性能 GPT-4 变体
- `gpt-3.5-turbo` - 快速且经济的选择

### 模型选择

您可以通过两种方式选择您偏好的模型：

1. **前端界面**：使用网络界面中的模型选择器从可用模型中选择
2. **默认配置**：系统根据模型名称自动检测提供商（例如，以"gpt-"开头的模型使用 OpenAI，以"gemini"开头的模型使用 Google Gemini）

## 后端智能体工作原理（高级概述）

后端的核心是在 `backend/src/agent/graph.py` 中定义的 LangGraph 智能体。它遵循以下步骤：

![Agent Flow](./agent.png)

1. **生成初始查询**：基于您的输入，使用 AI 模型生成一组初始搜索查询
2. **网络研究**：对于每个查询，使用 AI 模型与 Google Search API 查找相关网页
3. **反思和知识差距分析**：智能体分析搜索结果以确定信息是否充分或是否存在知识差距。它使用 AI 模型进行此反思过程
4. **迭代优化**：如果发现差距或信息不足，它会生成后续查询并重复网络研究和反思步骤（最多配置的最大循环次数）
5. **最终答案**：一旦研究被认为充分，智能体将收集的信息合成为连贯的答案，包括来自网络来源的引用，使用 AI 模型

## 部署

在生产环境中，后端服务器提供优化的静态前端构建。LangGraph 需要 Redis 实例和 Postgres 数据库。Redis 用作发布-订阅代理，以启用后台运行的流式实时输出。Postgres 用于存储助手、线程、运行、持久化线程状态和长期记忆，并管理具有"恰好一次"语义的后台任务队列状态。有关如何部署后端服务器的更多详细信息，请查看 [LangGraph 文档](https://langchain-ai.github.io/langgraph/concepts/deployment_options/)。

**1. 构建 Docker 镜像：**

从**项目根目录**运行以下命令：
```bash
docker build -t gemini-fullstack-langgraph -f Dockerfile .
```

**2. 运行生产服务器：**
```bash
GEMINI_API_KEY=<your_gemini_api_key> LANGSMITH_API_KEY=<your_langsmith_api_key> docker-compose up
```

打开浏览器并导航到 `http://localhost:8123/app/` 查看应用程序。API 将在 `http://localhost:8123` 可用。

## 使用的技术

- [React](https://reactjs.org/)（使用 [Vite](https://vitejs.dev/)）- 前端用户界面
- [Tailwind CSS](https://tailwindcss.com/) - 样式设计
- [Shadcn UI](https://ui.shadcn.com/) - 组件库
- [LangGraph](https://github.com/langchain-ai/langgraph) - 构建后端研究智能体
- [Google Gemini](https://ai.google.dev/models/gemini) - 用于查询生成、反思和答案合成的 LLM 提供商
- [OpenAI](https://openai.com/) - 支持 GPT 模型的替代 LLM 提供商
- [LangChain](https://langchain.com/) - LLM 集成和编排框架

## 许可证

此项目根据 Apache License 2.0 许可。有关详细信息，请参阅 [LICENSE](LICENSE) 文件。
