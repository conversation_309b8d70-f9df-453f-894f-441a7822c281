# AI Research Agent - Fullstack LangGraph Application

This project demonstrates a fullstack application using a React frontend and a LangGraph-powered backend agent. The agent is designed to perform comprehensive research on a user's query by dynamically generating search terms, querying the web using Google Search, reflecting on the results to identify knowledge gaps, and iteratively refining its search until it can provide a well-supported answer with citations. This application supports both **Google Gemini** and **OpenAI** models, allowing users to choose their preferred AI provider.

![Gemini Fullstack LangGraph](./app.png)

## Features

- 💬 Fullstack application with a React frontend and LangGraph backend.
- 🧠 Powered by a LangGraph agent for advanced research and conversational AI.
- 🤖 **Multi-Provider AI Support**: Choose between Google Gemini and OpenAI models.
- 🔍 Dynamic search query generation using configurable AI models.
- 🌐 Integrated web research via Google Search API.
- 🤔 Reflective reasoning to identify knowledge gaps and refine searches.
- 📄 Generates answers with citations from gathered sources.
- ⚙️ **Flexible Configuration**: Support for custom base URLs and API keys.
- 🔄 Hot-reloading for both frontend and backend development during development.

## Project Structure

The project is divided into two main directories:

- `frontend/`: Contains the React application built with Vite.
- `backend/`: Contains the LangGraph/FastAPI application, including the research agent logic.

## Getting Started: Development and Local Testing

Follow these steps to get the application running locally for development and testing.

**1. Prerequisites:**

- Node.js and npm (or yarn/pnpm)
- Python 3.8+
- **API Keys**: The backend agent requires at least one AI provider API key.

**API Configuration:**

1.  Navigate to the `backend/` directory.
2.  Create a file named `.env` by copying the `backend/.env.example` file.
3.  Configure your preferred AI provider(s):

**For Google Gemini (Default):**

```bash
GEMINI_API_KEY="YOUR_GEMINI_API_KEY"
```

**For OpenAI:**

```bash
OPENAI_API_KEY="YOUR_OPENAI_API_KEY"
# Optional: Custom base URL for OpenAI-compatible APIs
OPENAI_BASE_URL="https://api.openai.com/v1"
```

**For Both Providers:**

```bash
GEMINI_API_KEY="YOUR_GEMINI_API_KEY"
OPENAI_API_KEY="YOUR_OPENAI_API_KEY"
```

**2. Install Dependencies:**

**Backend:**

```bash
cd backend
pip install .
```

**Frontend:**

```bash
cd frontend
npm install
```

**3. Run Development Servers:**

**Backend & Frontend:**

```bash
make dev
```

This will run the backend and frontend development servers. Open your browser and navigate to the frontend development server URL (e.g., `http://localhost:5173/app`).

_Alternatively, you can run the backend and frontend development servers separately. For the backend, open a terminal in the `backend/` directory and run `langgraph dev`. The backend API will be available at `http://127.0.0.1:2024`. It will also open a browser window to the LangGraph UI. For the frontend, open a terminal in the `frontend/` directory and run `npm run dev`. The frontend will be available at `http://localhost:5173`._

## AI Model Selection

The application supports multiple AI providers and models:

### Available Models

**Google Gemini Models:**

- `gemini-2.0-flash` - Fast and efficient for query generation
- `gemini-2.5-flash-preview-04-17` - Enhanced reasoning capabilities
- `gemini-2.5-pro-preview-05-06` - Most advanced Gemini model

**OpenAI Models:**

- `gpt-4o` - Latest GPT-4 optimized model
- `gpt-4o-mini` - Faster, cost-effective version
- `gpt-4-turbo` - High-performance GPT-4 variant
- `gpt-3.5-turbo` - Fast and economical option

### Model Selection

You can choose your preferred model in two ways:

1. **Frontend Interface**: Use the model selector in the web interface to choose from available models.
2. **Default Configuration**: The system automatically detects the provider based on the model name (e.g., models starting with "gpt-" use OpenAI, models starting with "gemini" use Google Gemini).

## How the Backend Agent Works (High-Level)

The core of the backend is a LangGraph agent defined in `backend/src/agent/graph.py`. It follows these steps:

![Agent Flow](./agent.png)

1.  **Generate Initial Queries:** Based on your input, it generates a set of initial search queries using a Gemini model.
2.  **Web Research:** For each query, it uses the Gemini model with the Google Search API to find relevant web pages.
3.  **Reflection & Knowledge Gap Analysis:** The agent analyzes the search results to determine if the information is sufficient or if there are knowledge gaps. It uses a Gemini model for this reflection process.
4.  **Iterative Refinement:** If gaps are found or the information is insufficient, it generates follow-up queries and repeats the web research and reflection steps (up to a configured maximum number of loops).
5.  **Finalize Answer:** Once the research is deemed sufficient, the agent synthesizes the gathered information into a coherent answer, including citations from the web sources, using a Gemini model.

## Deployment

In production, the backend server serves the optimized static frontend build. LangGraph requires a Redis instance and a Postgres database. Redis is used as a pub-sub broker to enable streaming real time output from background runs. Postgres is used to store assistants, threads, runs, persist thread state and long term memory, and to manage the state of the background task queue with 'exactly once' semantics. For more details on how to deploy the backend server, take a look at the [LangGraph Documentation](https://langchain-ai.github.io/langgraph/concepts/deployment_options/). Below is an example of how to build a Docker image that includes the optimized frontend build and the backend server and run it via `docker-compose`.

_Note: For the docker-compose.yml example you need a LangSmith API key, you can get one from [LangSmith](https://smith.langchain.com/settings)._

_Note: If you are not running the docker-compose.yml example or exposing the backend server to the public internet, you update the `apiUrl` in the `frontend/src/App.tsx` file your host. Currently the `apiUrl` is set to `http://localhost:8123` for docker-compose or `http://localhost:2024` for development._

**1. Build the Docker Image:**

Run the following command from the **project root directory**:

```bash
docker build -t gemini-fullstack-langgraph -f Dockerfile .
```

**2. Run the Production Server:**

```bash
GEMINI_API_KEY=<your_gemini_api_key> LANGSMITH_API_KEY=<your_langsmith_api_key> docker-compose up
```

Open your browser and navigate to `http://localhost:8123/app/` to see the application. The API will be available at `http://localhost:8123`.

## Technologies Used

- [React](https://reactjs.org/) (with [Vite](https://vitejs.dev/)) - For the frontend user interface.
- [Tailwind CSS](https://tailwindcss.com/) - For styling.
- [Shadcn UI](https://ui.shadcn.com/) - For components.
- [LangGraph](https://github.com/langchain-ai/langgraph) - For building the backend research agent.
- [Google Gemini](https://ai.google.dev/models/gemini) - LLM provider for query generation, reflection, and answer synthesis.
- [OpenAI](https://openai.com/) - Alternative LLM provider with GPT models support.
- [LangChain](https://langchain.com/) - Framework for LLM integration and orchestration.

## License

This project is licensed under the Apache License 2.0. See the [LICENSE](LICENSE) file for details.
