import os
from pydantic import BaseModel, <PERSON>
from typing import Any, Optional, Literal

from langchain_core.runnables import RunnableConfig


class Configuration(BaseModel):
    """The configuration for the agent."""

    # AI Provider Configuration
    ai_provider: Literal["gemini", "openai"] = Field(
        default="gemini",
        metadata={
            "description": "The AI provider to use (gemini or openai)."
        },
    )

    # OpenAI Configuration
    openai_api_key: Optional[str] = Field(
        default=None,
        metadata={
            "description": "OpenAI API key. If not provided, will use OPENAI_API_KEY environment variable."
        },
    )

    openai_base_url: Optional[str] = Field(
        default=None,
        metadata={
            "description": "OpenAI API base URL. If not provided, will use default OpenAI endpoint."
        },
    )

    openai_model: str = Field(
        default="gpt-4o",
        metadata={
            "description": "OpenAI model name to use when ai_provider is 'openai'."
        },
    )

    # Gemini Configuration (existing)
    gemini_api_key: Optional[str] = Field(
        default=None,
        metadata={
            "description": "Gemini API key. If not provided, will use GEMINI_API_KEY environment variable."
        },
    )

    query_generator_model: str = Field(
        default="gemini-2.0-flash",
        metadata={
            "description": "The name of the language model to use for the agent's query generation."
        },
    )

    reflection_model: str = Field(
        default="gemini-2.5-flash-preview-04-17",
        metadata={
            "description": "The name of the language model to use for the agent's reflection."
        },
    )

    answer_model: str = Field(
        default="gemini-2.5-pro-preview-05-06",
        metadata={
            "description": "The name of the language model to use for the agent's answer."
        },
    )

    number_of_initial_queries: int = Field(
        default=3,
        metadata={"description": "The number of initial search queries to generate."},
    )

    max_research_loops: int = Field(
        default=2,
        metadata={"description": "The maximum number of research loops to perform."},
    )

    def get_effective_api_key(self, provider: str) -> str:
        """Get the effective API key for the specified provider."""
        if provider == "openai":
            return self.openai_api_key or os.environ.get("OPENAI_API_KEY")
        elif provider == "gemini":
            return self.gemini_api_key or os.environ.get("GEMINI_API_KEY")
        else:
            raise ValueError(f"Unsupported provider: {provider}")

    def get_effective_base_url(self, provider: str) -> Optional[str]:
        """Get the effective base URL for the specified provider."""
        if provider == "openai":
            return self.openai_base_url or os.environ.get("OPENAI_BASE_URL")
        elif provider == "gemini":
            return None  # Gemini doesn't support custom base URLs
        else:
            raise ValueError(f"Unsupported provider: {provider}")

    def determine_provider_from_model(self, model_name: str) -> str:
        """Determine the AI provider based on the model name."""
        if model_name.startswith(("gpt-", "o1-", "text-", "davinci", "curie", "babbage", "ada")):
            return "openai"
        elif model_name.startswith("gemini"):
            return "gemini"
        else:
            # Default to the configured provider
            return self.ai_provider

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )

        # Get raw values from environment or config
        raw_values: dict[str, Any] = {
            name: os.environ.get(name.upper(), configurable.get(name))
            for name in cls.model_fields.keys()
        }

        # Filter out None values
        values = {k: v for k, v in raw_values.items() if v is not None}

        return cls(**values)
